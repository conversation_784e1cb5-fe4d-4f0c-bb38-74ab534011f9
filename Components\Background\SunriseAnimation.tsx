import React, { useLayoutEffect, useRef, forwardRef, useImperativeHandle } from 'react';
import { gsap } from 'gsap';

// Interface pour les méthodes exposées du composant
export interface SunriseAnimationRef {
  triggerSunrise: () => void;
  resetSun: () => void;
}

// Interface pour les props du composant
interface SunriseAnimationProps {
  isVisible: boolean; // Contrôle la visibilité du composant
}

const SunriseAnimation = forwardRef<SunriseAnimationRef, SunriseAnimationProps>(
  ({ isVisible }, ref) => {
    // Références pour les éléments DOM
    const containerRef = useRef<HTMLDivElement>(null);
    const sunWrapperRef = useRef<HTMLDivElement>(null);
    const sunHaloRef = useRef<HTMLDivElement>(null);
    const lensFlareRef = useRef<HTMLDivElement>(null);
    const sunImageRef = useRef<HTMLImageElement>(null);
    // 🌟 CISCO: Nouvelle référence pour les rayons du soleil !
    const sunRaysRef = useRef<HTMLDivElement>(null);

    // Référence pour la timeline GSAP
    const timelineRef = useRef<gsap.core.Timeline | null>(null);

    // 🌅 FONCTION PRINCIPALE: Déclencher l'animation de lever de soleil
    const triggerSunrise = () => {
      if (!sunWrapperRef.current || !sunHaloRef.current || !lensFlareRef.current || !sunRaysRef.current) {
        console.warn('🌅 Éléments DOM non prêts pour l\'animation');
        return;
      }

      console.log('🌅 Déclenchement de l\'animation de lever de soleil');

      // Nettoyer l'animation précédente si elle existe
      if (timelineRef.current) {
        timelineRef.current.kill();
      }

      // Créer une nouvelle timeline
      timelineRef.current = gsap.timeline({
        onComplete: () => {
          console.log('✨ Animation de lever de soleil terminée');
        }
      });

      // 🔧 CISCO: Position initiale - soleil visible mais plus haut (100px de moins)
      gsap.set(sunWrapperRef.current, {
        y: '60%', // Position initiale plus haute selon mesures Cisco
        opacity: 1
      });
      gsap.set(sunHaloRef.current, {
        opacity: 0,
        scale: 0.3
      });
      gsap.set(lensFlareRef.current, {
        opacity: 0,
        scale: 0.2,
        y: '50%'
      });
      // 🌟 CISCO: Position initiale des rayons du soleil
      gsap.set(sunRaysRef.current, {
        opacity: 0,
        scale: 0.5,
        rotation: 0
      });

      // 🌅 PHASE 1: Le soleil se lève progressivement (CISCO: Plus lent pour effet Hollywood)
      timelineRef.current.fromTo(
        sunWrapperRef.current,
        { y: '60%' },
        {
          y: '20%', // Monte avec ~80px de débattement selon mesures Cisco
          duration: 24.0, // CISCO: Ralenti de 16s à 24s pour effet plus majestueux
          ease: 'power1.out' // CISCO: Easing plus doux pour mouvement naturel
        }
      );

      // 🌟 PHASE 2: Le halo apparaît et s'intensifie (CISCO: Synchronisé avec animation ralentie)
      timelineRef.current.fromTo(
        sunHaloRef.current,
        { opacity: 0, scale: 0.3 },
        {
          opacity: 0.6, // CISCO: Plus transparent pour effet subtil mais puissant
          scale: 3.5, // CISCO: HALO GÉANT pour "casser la baraque" !
          duration: 18.0, // CISCO: Synchronisé avec nouvelle durée
          ease: 'power1.inOut' // CISCO: Easing plus doux
        },
        3 // CISCO: Démarre après 3 secondes pour build-up plus dramatique
      );

      // ✨ PHASE 3: Le lens flare apparaît avec décalage (CISCO: Timing Hollywood)
      timelineRef.current.fromTo(
        lensFlareRef.current,
        { opacity: 0, y: '50%', scale: 0.2 },
        {
          opacity: 0.9, // CISCO: Lens flare plus magistral et prononcé
          y: '0%',
          scale: 1.4, // CISCO: Encore plus grand pour effet spectaculaire
          duration: 15.0, // CISCO: Plus long pour effet dramatique
          ease: 'power1.out' // CISCO: Easing plus doux
        },
        6 // CISCO: Démarre après 6 secondes pour crescendo dramatique
      );

      // 🌟 CISCO: PHASE 4 - LES RAYONS DU SOLEIL ! (Mission "impossible" réalisée !)
      timelineRef.current.fromTo(
        sunRaysRef.current,
        { opacity: 0, scale: 0.5, rotation: 0 },
        {
          opacity: 0.8, // CISCO: Rayons plus intenses pour suivre le halo géant
          scale: 1.5, // CISCO: Rayons agrandis pour "défoncer" l'effet !
          duration: 12.0, // CISCO: Apparition progressive des rayons
          ease: 'power1.inOut'
        },
        8 // CISCO: Démarre après 8 secondes quand le soleil est bien visible
      );

      // 🌟 CISCO: PHASE 5 - ROTATION CONTINUE DES RAYONS (Effet Hollywood ultime !)
      timelineRef.current.to(
        sunRaysRef.current,
        {
          rotation: 360, // CISCO: Rotation complète pour effet dynamique
          duration: 60.0, // CISCO: Rotation lente et majestueuse (1 minute)
          ease: 'none', // CISCO: Vitesse constante pour rotation naturelle
          repeat: -1 // CISCO: Rotation infinie pour effet hypnotique
        },
        8 // CISCO: Commence en même temps que l'apparition
      );
    };

    // 🔄 FONCTION: Remettre le soleil en position initiale
    const resetSun = () => {
      if (timelineRef.current) {
        timelineRef.current.kill();
      }

      if (sunWrapperRef.current && sunHaloRef.current && lensFlareRef.current && sunRaysRef.current) {
        gsap.set([sunWrapperRef.current, sunHaloRef.current, lensFlareRef.current], {
          y: '60%', // Position initiale corrigée
          opacity: 0,
          scale: 0.3
        });
        // 🌟 CISCO: Reset des rayons du soleil
        gsap.set(sunRaysRef.current, {
          opacity: 0,
          scale: 0.5,
          rotation: 0
        });
      }

      console.log('🔄 Soleil remis en position initiale');
    };

    // Exposer les méthodes via useImperativeHandle
    useImperativeHandle(ref, () => ({
      triggerSunrise,
      resetSun
    }));

    // Cleanup à la destruction du composant
    useLayoutEffect(() => {
      return () => {
        if (timelineRef.current) {
          timelineRef.current.kill();
        }
      };
    }, []);

    // Ne pas rendre si non visible
    if (!isVisible) {
      return null;
    }

    return (
      <div
        ref={containerRef}
        className="fixed inset-0 w-full h-full pointer-events-none"
        style={{ zIndex: 1.8 }} // 🔧 CISCO: Entre étoiles (1) et nuages (2,10,12) - Soleil derrière les nuages
      >
        {/* Conteneur pour le soleil et ses effets */}
        <div
          ref={sunWrapperRef}
          className="absolute w-48 h-48 left-1/2 top-1/2 -translate-x-1/2"
          style={{
            transform: 'translateX(-50%) translateY(60%)', // Position initiale plus haute (100px de moins)
          }}
        >
          <div className="relative w-full h-full">
            {/* EFFET 1: Le Halo lumineux */}
            <div
              ref={sunHaloRef}
              className="sun-halo absolute inset-0 opacity-0"
            />

            {/* EFFET 2: Le Lens Flare */}
            <div
              ref={lensFlareRef}
              className="lens-flare absolute inset-[-200%] opacity-0"
            />

            {/* 🌟 CISCO: EFFET 3 - LES RAYONS DU SOLEIL ! (Mission "impossible" accomplie !) */}
            <div
              ref={sunRaysRef}
              className="sun-rays absolute inset-[-300%] opacity-0"
            />

            {/* L'image du soleil */}
            <img
              ref={sunImageRef}
              src="/SUN.png"
              alt="Soleil"
              className="relative z-10 w-full h-full object-contain"
              style={{
                filter: 'drop-shadow(0 0 20px rgba(255, 220, 0, 0.6))'
              }}
            />
          </div>
        </div>
      </div>
    );
  }
);

SunriseAnimation.displayName = 'SunriseAnimation';

export default SunriseAnimation;
